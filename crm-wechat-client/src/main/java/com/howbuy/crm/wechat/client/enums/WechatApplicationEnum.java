/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 企业微信-内部应用-枚举
 * NOTICE :  替换枚举： WechatAppEnum 和 WechatSecretEnum
 * <AUTHOR>
 * @date 2024/9/19 15:29
 * @since JDK 1.8
 */
public enum WechatApplicationEnum {

    //TODO: 重构计划标记：
    // 应用的枚举 删除属性：companyNoEnum 。  在 ApplicationUtilityDTO 中获取

    /***********************************好买财富 ******************************************************/
    /**
     * [好买财富]企业微信-客户联系
     */
    WEALTH_CUSTOMER("wealth_customer", "好买财富-客户联系", "1"),

    /**
     * [好买财富]企业微信-自建应用-crm
     */
    WEALTH_CRM("wealth_crm", "好买财富-自建应用-CRM", "1"),

    /**
     * [好买财富]企业微信--商路通
     */
    WEALTH_RHSLT("wealth_rhslt", "好买财富-商路通", "1"),

    /**
     * [好买财富]企业微信-自建应用-画像
     */
    WEALTH_PORTRAIT("wealth_portrait", "好买财富-自建应用-画像", "1"),


    /**
     * [好买财富]企业微信-机构服务通知
     */
    ORG_SERVICE_NOTICE("org_service_notice","好买财富-机构服务通知", "1"),


    /***********************************好买财富 ******************************************************/





    /***********************************好买基金 ******************************************************/
    /**
     * [好买基金]企业微信-客户联系
     */
    FUND_CUSTOMER("fund_customer", "好买基金-客户联系", "2"),

    /***********************************好买基金 ******************************************************/




    /***********************************好晓买 ******************************************************/

    /**
     * [好晓买]企业微信-客户联系
     */
    HXM_CUSTOMER("hxm_customer", "好晓买-客户联系", "3");
    /***********************************好晓买 ******************************************************/

    /**
     * 编码
     */
    private String code;


    /**
     * 描述
     */
    private String description;


    /**
     * 企业编码
     */
    private String companyNo;

    /**
     * 公司编号枚举（兼容性保留，逐步废弃）
     * @deprecated 建议使用companyNo字段
     */
    @Deprecated
    private CompanyNoEnum companyNoEnum;

    WechatApplicationEnum(String code, String description, String companyNo) {
        this.code = code;
        this.description = description;
        this.companyNo = companyNo;
        // 兼容性转换
        this.companyNoEnum = CompanyNoEnum.getEnum(companyNo);
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 获取企业编码
     * @return 企业编码
     */
    public String getCompanyNo() {
        return companyNo;
    }

    /**
     * 获取企业编码枚举（兼容性保留）
     * @return 企业编码枚举
     * @deprecated 建议使用getCompanyNo()方法
     */
    @Deprecated
    public CompanyNoEnum getCompanyNoEnum() {
        return companyNoEnum;
    }


    /**
     * 根据编码获取枚举
     * @param code
     * @return
     */
    public static WechatApplicationEnum getEnum(String code) {
        for (WechatApplicationEnum value : WechatApplicationEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }


    /**
     * 根据企业编码获取应用枚举列表
     * @param companyNo 企业编码
     * @return 应用枚举列表
     */
    public static List<WechatApplicationEnum> getApplicationEnums(String companyNo) {
        List<WechatApplicationEnum> list = new ArrayList<>();
        for (WechatApplicationEnum value : WechatApplicationEnum.values()) {
            if (value.getCompanyNo().equals(companyNo)) {
                list.add(value);
            }
        }
        return list;
    }

    /**
     * 根据公司编号获取应用枚举列表
     * @param companyNoEnum 企业编码枚举
     * @return 应用枚举列表
     * @deprecated 请使用 {@link #getApplicationEnums(String)} 方法
     */
    @Deprecated
    public static List<WechatApplicationEnum> getApplicationEnums(CompanyNoEnum companyNoEnum) {
        if (companyNoEnum == null) {
            return new ArrayList<>();
        }
        return getApplicationEnums(companyNoEnum.getCode());
    }



}
